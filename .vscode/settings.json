{
  // 配置语言的文件关联
  "files.associations": {
    "pages.json": "jsonc", // pages.json 可以写注释
    "manifest.json": "jsonc" // manifest.json 可以写注释
  },

  "stylelint.enable": false, // 禁用 stylelint
  "css.validate": false, // 禁用 CSS 内置验证
  "scss.validate": false, // 禁用 SCSS 内置验证
  "less.validate": false, // 禁用 LESS 内置验证

  "typescript.tsdk": "node_modules\\typescript\\lib",
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    "README.md": "index.html,favicon.ico,robots.txt,CHANGELOG.md",
    "pages.config.ts": "manifest.config.ts,openapi-ts-request.config.ts",
    "package.json": "tsconfig.json,pnpm-lock.yaml,pnpm-workspace.yaml,LICENSE,.gitattributes,.gitignore,.gitpod.yml,CNAME,.npmrc,.browserslistrc",
    "eslint.config.mjs": ".commitlintrc.*,.prettier*,.editorconfig,.commitlint.cjs,.eslint*"
  },

  // ==================== TypeScript 开发者友好配置 ====================
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.suggest.autoImports": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.updateImportsOnFileMove.enabled": "always",

  // 类型提示配置 - 帮助开发者理解类型
  "typescript.inlayHints.parameterNames.enabled": "all",
  "typescript.inlayHints.variableTypes.enabled": true,
  "typescript.inlayHints.functionLikeReturnTypes.enabled": true,
  "typescript.inlayHints.parameterTypes.enabled": true,

  // 降低 TypeScript 错误严重程度，避免阻塞开发
  "typescript.reportStyleChecksAsWarnings": true,
  "typescript.preferences.quoteStyle": "single",

  // JavaScript 支持配置
  "javascript.preferences.includePackageJsonAutoImports": "auto",
  "javascript.suggest.autoImports": true,
  "javascript.updateImportsOnFileMove.enabled": "always",
  "javascript.validate.enable": true,

  // 智能提示配置
  "typescript.suggest.includeAutomaticOptionalChainCompletions": true,
  "javascript.suggest.includeAutomaticOptionalChainCompletions": true,

  // Disable the default formatter, use eslint instead
  "prettier.enable": false,
  "editor.formatOnSave": false,

  // Auto fix
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },

  // Silent the stylistic rules in you IDE, but still auto fix them
  "eslint.rules.customizations": [
    { "rule": "style/*", "severity": "off", "fixable": true },
    { "rule": "format/*", "severity": "off", "fixable": true },
    { "rule": "*-indent", "severity": "off", "fixable": true },
    { "rule": "*-spacing", "severity": "off", "fixable": true },
    { "rule": "*-spaces", "severity": "off", "fixable": true },
    { "rule": "*-order", "severity": "off", "fixable": true },
    { "rule": "*-dangle", "severity": "off", "fixable": true },
    { "rule": "*-newline", "severity": "off", "fixable": true },
    { "rule": "*quotes", "severity": "off", "fixable": true },
    { "rule": "*semi", "severity": "off", "fixable": true }
  ],

  // Enable eslint for all supported languages
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "yaml",
    "toml",
    "xml",
    "gql",
    "graphql",
    "astro",
    "svelte",
    "css",
    "less",
    "scss",
    "pcss",
    "postcss"
  ],
  "cSpell.words": [
    "alova",
    "Aplipay",
    "attributify",
    "chooseavatar",
    "climblee",
    "commitlint",
    "dcloudio",
    "iconfont",
    "oxlint",
    "qrcode",
    "refresherrefresh",
    "scrolltolower",
    "tabbar",
    "Toutiao",
    "uniapp",
    "unibest",
    "unocss",
    "uview",
    "uvui",
    "Wechat",
    "WechatMiniprogram",
    "Weixin"
  ],

  // ==================== 开发者体验优化 ====================

  // 编辑器增强
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": true
  },
  "editor.suggestSelection": "first",
  "editor.tabCompletion": "on",
  "editor.parameterHints.enabled": true,
  "editor.hover.enabled": true,
  "editor.hover.delay": 300,

  // 错误和警告显示
  "problems.decorations.enabled": true,
  "editor.renderValidationDecorations": "on",

  // 代码折叠和导航
  "editor.foldingStrategy": "indentation",
  "editor.showFoldingControls": "always",
  "editor.matchBrackets": "always",
  "editor.bracketPairColorization.enabled": true,

  // Vue 开发增强
  "emmet.includeLanguages": {
    "vue-html": "html",
    "vue": "html"
  },

  // 文件性能优化
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true
  },

  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/*.log": true,
    "**/pnpm-lock.yaml": true
  },

  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/dist/**": true
  },

  // 调试配置
  "debug.allowBreakpointsEverywhere": true,
  "debug.inlineValues": "auto",

  // Git 配置
  "git.enableSmartCommit": true,
  "git.confirmSync": false,
  "git.autofetch": true,

  // 语言特定格式化配置
  "[vue]": {
    "editor.defaultFormatter": "Vue.volar"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.wordWrap": "on"
  }
}
